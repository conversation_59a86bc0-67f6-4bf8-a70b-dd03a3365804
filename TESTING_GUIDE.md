# 对话管理功能测试指南

## 测试场景

### 1. 延迟创建对话测试
**预期行为**: 当没有对话时，用户发送第一条消息时自动创建对话

**测试步骤**:
1. 清空数据库或确保没有现有对话
2. 打开应用 (http://localhost:5174)
3. 应该显示空白聊天界面，没有自动创建对话
4. 输入第一条消息并发送
5. 应该自动创建对话并更新URL

**验证点**:
- [ ] 初始状态没有自动创建对话
- [ ] 发送第一条消息后创建对话
- [ ] URL更新为 `?session=<session-id>`
- [ ] 侧边栏显示新创建的对话

### 2. 新建对话功能测试
**预期行为**: 点击 "New Chat" 按钮时，应该创建新对话并正确跳转

**测试步骤**:
1. 在现有对话中发送一些消息
2. 点击侧边栏的 "New Chat" 按钮
3. 应该创建新对话并更新URL

**验证点**:
- [ ] 侧边栏新增一个 "New Chat" 对话
- [ ] URL更新为新的 `?session=<new-session-id>`
- [ ] 新对话被自动选中（高亮显示）
- [ ] 右侧聊天区域被清空，显示 "Start a conversation"
- [ ] 输入框为空且可用

### 3. 自动命名功能测试
**预期行为**: 完成第一轮对话后，自动调用GPT生成对话标题

**测试步骤**:
1. 创建新对话
2. 发送第一条消息，例如："什么是React？"
3. 等待AI回复完成
4. 等待约5秒钟（自动命名有延迟）
5. 观察侧边栏中的对话标题是否更新

**验证点**:
- [ ] 对话标题从 "New Chat" 更新为相关的描述性标题
- [ ] 标题长度不超过50个字符
- [ ] 标题准确反映对话内容

### 4. 手动重命名功能测试
**预期行为**: 用户可以手动编辑对话标题

**测试步骤**:
1. 悬停在侧边栏的任一对话上
2. 点击出现的编辑图标（铅笔图标）
3. 修改标题文本
4. 按 Enter 保存或按 Esc 取消

**验证点**:
- [ ] 悬停时显示编辑和删除图标
- [ ] 点击编辑图标后出现输入框
- [ ] 可以修改标题文本
- [ ] Enter 键保存更改
- [ ] Esc 键取消更改
- [ ] 保存后标题立即更新

### 5. 对话切换功能测试
**预期行为**: 点击不同对话时正确加载对话历史并更新URL

**测试步骤**:
1. 创建多个对话，每个对话发送不同的消息
2. 在对话之间切换
3. 验证每个对话的消息历史正确显示
4. 验证URL正确更新

**验证点**:
- [ ] 点击对话时URL更新为 `?session=<session-id>`
- [ ] 点击对话时正确高亮选中
- [ ] 右侧显示正确的消息历史
- [ ] 消息顺序正确
- [ ] 工具调用状态正确显示
- [ ] 直接访问带session参数的URL能正确加载对话

### 6. URL路由管理测试
**预期行为**: 所有对话操作都通过URL管理，支持浏览器前进后退

**测试步骤**:
1. 创建几个对话并在它们之间切换
2. 使用浏览器的前进/后退按钮
3. 直接在地址栏输入 `?session=<session-id>`
4. 删除当前选中的对话

**验证点**:
- [ ] 浏览器前进/后退按钮正确切换对话
- [ ] 直接访问URL能正确加载对应对话
- [ ] 删除当前对话后URL清空（回到首页）
- [ ] 无效的session ID显示空白界面
- [ ] URL状态与UI状态始终同步

## 数据库验证

可以使用 Prisma Studio 查看数据库状态：
```bash
npx prisma studio
```

**检查项目**:
- [ ] ChatSession 表中有正确的对话记录
- [ ] Message 表中有完整的消息记录
- [ ] 工具调用相关字段正确保存
- [ ] 对话标题正确更新

## 常见问题排查

### 问题1: 自动创建对话不工作
- 检查浏览器控制台是否有错误
- 确认 `/api/sessions` 端点正常工作
- 检查数据库连接

### 问题2: 新建对话后内容没有清空
- 检查 `clearMessages` 函数是否被调用
- 确认 `handleNewSession` 逻辑正确

### 问题3: 自动命名不工作
- 检查 OpenAI API 配置（确保使用 `openaiClient` 而不是 `openai`）
- 确认 `/api/sessions/:id/generate-title` 端点正常
- 查看控制台日志
- 验证 OPENAI_API_KEY 环境变量设置正确

### 问题4: 手动重命名不保存
- 检查 `updateSessionTitle` 函数
- 确认 PATCH `/api/sessions/:id` 端点正常
- 检查数据库更新

### 问题5: 创建对话后侧边栏未更新
- 确认 `onSessionCreated` 回调中调用了 `loadSessions()`
- 检查 `createSession` 函数是否正确返回 session ID
- 验证网络请求是否成功

### 问题6: OpenAI API 调用失败
- 确认使用 `openaiClient` 而不是 `openai` 进行 API 调用
- 检查 OPENAI_API_KEY 环境变量
- 验证 OpenAI 服务配置

## 性能注意事项

- 自动刷新间隔设置为5秒，可根据需要调整
- 自动命名有1秒延迟，确保消息已保存
- 大量对话时考虑分页加载
