import { streamText, tool } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 创建 OpenAI 客户端
const openai = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// 简单的测试工具
const testTool = tool({
  description: '测试工具，返回当前时间',
  inputSchema: z.object({
    message: z.string().describe('要处理的消息'),
  }),
  execute: async ({ message }) => {
    console.log('Tool executed with message:', message);
    return `处理消息: ${message}，当前时间: ${new Date().toISOString()}`;
  },
});

async function testStreamText() {
  console.log('开始测试 streamText 工具调用...');
  
  const result = streamText({
    model: openai('gpt-4o-mini'),
    system: "你是一个助手。当用户询问时间时，使用工具获取当前时间。在使用工具后，你必须提供一个基于工具结果的完整回复。永远不要只用工具调用结束回复 - 总是跟进解释性文本。",
    messages: [
      { role: 'user', content: '现在几点了？' }
    ],
    tools: {
      testTool,
    },
  });

  console.log('开始处理流...');
  
  let hasToolCalls = false;
  let hasTextAfterTools = false;

  for await (const part of result.fullStream) {
    console.log('Stream part:', part.type);

    if (part.type === 'tool-call') {
      console.log('工具调用:', part.toolName, part.input);
      hasToolCalls = true;
    } else if (part.type === 'tool-result') {
      console.log('工具结果:', part.toolName, part.output);
    } else if (part.type === 'text-delta') {
      console.log('文本增量:', part.text);
      if (hasToolCalls) {
        hasTextAfterTools = true;
      }
    } else if (part.type === 'finish') {
      console.log('流结束:', part.finishReason);
      console.log('有工具调用:', hasToolCalls);
      console.log('工具调用后有文本:', hasTextAfterTools);
      break;
    } else if (part.type === 'error') {
      console.error('流错误:', part.error);
      break;
    } else {
      console.log('其他类型:', part.type);
    }
  }
  
  console.log('测试完成');
}

testStreamText().catch(console.error);
