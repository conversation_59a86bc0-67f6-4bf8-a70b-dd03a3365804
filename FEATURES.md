# Chat Bot - 新功能说明

## 已实现的功能

### 1. 响应式UI设计
- ✅ 全新的响应式布局，支持桌面端和移动端
- ✅ 侧边栏设计，可在移动端折叠
- ✅ 现代化的Tailwind CSS样式
- ✅ 优化的消息显示界面

### 2. 消息处理逻辑抽象
- ✅ 创建了 `useChatMessages` Hook 来管理消息状态
- ✅ 创建了 `useChatSessions` Hook 来管理对话会话
- ✅ 分离了UI逻辑和业务逻辑

### 3. 对话管理功能
- ✅ **URL路由管理**: 对话ID作为URL查询参数，支持浏览器前进后退和直接访问
- ✅ **延迟创建对话**: 用户发送第一条消息时才创建对话，避免空对话
- ✅ **新建对话功能**: 点击New Chat创建新对话并通过URL跳转
- ✅ **自动命名功能**: 第一轮对话完成后自动调用GPT-4o-mini生成描述性标题
- ✅ **手动重命名功能**: 悬停显示编辑图标，支持实时编辑对话标题
- ✅ 对话历史记录保存
- ✅ 侧边栏显示历史对话
- ✅ 删除对话功能（删除当前对话时自动清空URL）
- ✅ 对话会话切换（通过URL状态管理）

### 4. 工具调用状态优化
- ✅ 工具调用完成后自动折叠
- ✅ 工具调用记录保留在对话中
- ✅ 后端过滤发送给LLM的消息（只发送user、assistant、system消息）
- ✅ 工具调用详情可展开/折叠查看

### 5. 数据库Schema更新
- ✅ 更新了Prisma schema支持新字段：
  - `ChatSession.title` - 对话标题
  - `ChatSession.updatedAt` - 更新时间
  - `Message.toolName` - 工具名称
  - `Message.toolArgs` - 工具参数
  - `Message.toolResult` - 工具结果
  - `Message.isCollapsed` - 折叠状态

### 6. 后端API增强
- ✅ `/api/sessions` - 获取所有对话会话
- ✅ `/api/sessions` (POST) - 创建新对话
- ✅ `/api/sessions/:id` (PATCH) - 更新对话标题
- ✅ `/api/sessions/:id` (DELETE) - 删除对话
- ✅ `/api/sessions/:id/messages` - 获取对话消息
- ✅ `/api/sessions/:id/generate-title` (POST) - 自动生成对话标题
- ✅ 聊天API支持会话ID和消息保存

## 技术特性

### 前端
- React Hooks 架构
- Tailwind CSS 响应式设计
- TypeScript 类型安全
- 实时流式响应处理

### 后端
- Prisma ORM 数据库管理
- SQLite 数据库
- 流式API响应
- 消息过滤和处理

### 数据流
1. 用户发送消息 → 保存到数据库
2. 后端过滤消息（只发送user/assistant/system给LLM）
3. LLM响应和工具调用 → 保存到数据库
4. 前端实时显示响应和工具状态
5. 工具调用完成后自动折叠

## 使用说明

1. **开始聊天**: 打开应用后直接输入消息，系统会自动创建对话并更新URL
2. **新建对话**: 点击侧边栏的"New Chat"按钮，创建新对话并通过URL跳转
3. **对话切换**: 点击侧边栏的对话记录，通过URL切换并加载完整消息历史
4. **URL导航**: 支持浏览器前进后退，可直接访问 `?session=<id>` 加载特定对话
5. **自动命名**: 完成第一轮对话后，系统会自动生成描述性标题
6. **手动重命名**: 悬停在对话上，点击编辑图标，支持Enter保存、Esc取消
7. **删除对话**: 悬停在对话上，点击删除图标（删除当前对话会清空URL）
8. **查看工具调用**: 工具调用会自动折叠，点击可展开查看详情
9. **移动端使用**: 点击汉堡菜单图标打开/关闭侧边栏

## 开发命令

```bash
# 启动开发服务器
npm run dev

# 数据库迁移
npx prisma migrate dev

# 生成Prisma客户端
npx prisma generate

# 查看数据库
npx prisma studio
```
