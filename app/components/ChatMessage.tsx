import { useState } from 'react';
import type { ChatMessage } from '~/hooks/useChatMessages';

interface ChatMessageProps {
  message: ChatMessage;
  index: number;
  onToggleCollapse?: (index: number) => void;
}

export function ChatMessageComponent({ message, index, onToggleCollapse }: ChatMessageProps) {
  const [isExpanded, setIsExpanded] = useState(!message.isCollapsed);

  const handleToggleCollapse = () => {
    setIsExpanded(!isExpanded);
    onToggleCollapse?.(index);
  };

  const formatToolArgs = (args: any) => {
    if (!args) return '';
    try {
      return JSON.stringify(args, null, 2);
    } catch {
      return String(args);
    }
  };

  const formatToolResult = (result: any) => {
    if (!result) return '';
    try {
      if (typeof result === 'string') {
        // Try to parse if it's a JSON string
        try {
          const parsed = JSON.parse(result);
          return JSON.stringify(parsed, null, 2);
        } catch {
          return result;
        }
      }
      return JSON.stringify(result, null, 2);
    } catch {
      return String(result);
    }
  };

  if (message.role === 'user') {
    return (
      <div className="flex justify-end mb-4">
        <div className="max-w-[70%] bg-blue-600 text-white rounded-lg px-4 py-2">
          <div className="text-sm font-medium mb-1">You</div>
          <div className="whitespace-pre-wrap">{message.content}</div>
        </div>
      </div>
    );
  }

  if (message.role === 'assistant') {
    return (
      <div className="flex justify-start mb-4">
        <div className="max-w-[70%] bg-gray-100 text-gray-900 rounded-lg px-4 py-2">
          <div className="text-sm font-medium mb-1 text-gray-700">Assistant</div>
          <div className="whitespace-pre-wrap">{message.content}</div>
        </div>
      </div>
    );
  }

  if (message.role === 'tool_call') {
    return (
      <div className="flex justify-start mb-4">
        <div className="max-w-[70%] bg-yellow-50 border border-yellow-200 rounded-lg px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="text-sm font-medium text-yellow-800">
                🔧 Tool Call: {message.toolName}
              </div>
              {message.toolResult && (
                <span className="text-xs text-green-600 font-medium">✓ Completed</span>
              )}
            </div>
            {message.toolResult && (
              <button
                onClick={handleToggleCollapse}
                className="text-yellow-600 hover:text-yellow-800 transition-colors"
                title={isExpanded ? 'Collapse' : 'Expand'}
              >
                <svg 
                  className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            )}
          </div>
          
          {isExpanded && (
            <div className="mt-2 space-y-2">
              {message.toolArgs && (
                <div>
                  <div className="text-xs font-medium text-yellow-700 mb-1">Arguments:</div>
                  <pre className="text-xs bg-yellow-100 p-2 rounded border overflow-x-auto">
                    {formatToolArgs(message.toolArgs)}
                  </pre>
                </div>
              )}
              
              {message.toolResult && (
                <div>
                  <div className="text-xs font-medium text-green-700 mb-1">Result:</div>
                  <pre className="text-xs bg-green-50 p-2 rounded border overflow-x-auto max-h-40 overflow-y-auto">
                    {formatToolResult(message.toolResult)}
                  </pre>
                </div>
              )}
            </div>
          )}
          
          {!isExpanded && message.toolResult && (
            <div className="mt-2 text-xs text-yellow-600">
              Click to expand tool details
            </div>
          )}
        </div>
      </div>
    );
  }

  return null;
}
